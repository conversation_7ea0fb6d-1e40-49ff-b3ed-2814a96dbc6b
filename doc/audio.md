# 间歇流音频系统配置文档

## 概述

间歇流应用的音频系统采用简化的YAML配置文件架构，专注于核心需求：每个主题包含3个音频文件，对应3个关键时间点。系统设计遵循以下原则：

- **简洁性**: 每个主题只需3个音频文件，配置简单明了
- **实用性**: 专注于实际使用的音频事件，避免冗余配置
- **易维护性**: 集中化的YAML配置文件管理
- **向后兼容**: 与现有设置系统无缝集成

## 音频事件

应用中定义了三个关键的音频事件：

### 1. 小休息开始 (break_start)
- **触发时机**: 每3-5分钟随机触发小休息时
- **音频特点**: 轻柔、温和的提示音
- **时长要求**: 1-3秒
- **音量范围**: 50%-80%

### 2. 小休息结束 (break_end)
- **触发时机**: 8-10秒小休息结束时
- **音频特点**: 清脆、明亮的回归提示音
- **时长要求**: 1-3秒
- **音量范围**: 60%-90%

### 3. 阶段结束 (session_complete)
- **触发时机**: 90分钟专注周期完成时
- **音频特点**: 积极、庆祝性的音效
- **时长要求**: 3-8秒
- **音量范围**: 70%-100%

## 目录结构

```
assets/audio/
├── audio_config.yaml          # 主配置文件
├── default/                   # 默认主题（优先级最高）
│   ├── break_start.mp3        # 小休息开始
│   ├── break_end.mp3          # 小休息结束
│   └── session_complete.mp3   # 90分钟阶段结束
├── nature/                    # 自然音主题
│   ├── water_drop.mp3         # 小休息开始（水滴声）
│   ├── wind_chime.mp3         # 小休息结束（风铃声）
│   └── forest_birds.mp3       # 阶段结束（森林鸟鸣）
├── synthetic/                 # 合成音主题
│   ├── soft_bell.mp3          # 小休息开始（柔和铃声）
│   ├── clear_ping.mp3         # 小休息结束（清脆提示音）
│   └── success_melody.mp3     # 阶段结束（成功旋律）
└── asmr/                      # ASMR主题
    ├── soft_whisper.mp3       # 小休息开始（轻柔耳语）
    ├── keyboard_soft.mp3      # 小休息结束（键盘敲击）
    └── completion_whisper.mp3 # 阶段结束（完成耳语）
```

## 配置文件说明

### 主配置文件 (audio_config.yaml)

简化的配置文件采用YAML格式，结构清晰简洁：

#### 主题定义
```yaml
themes:
  # 默认主题
  default:
    name: "默认"
    break_start: "default/break_start.mp3"      # 小休息开始
    break_end: "default/break_end.mp3"          # 小休息结束
    session_complete: "default/session_complete.mp3"  # 90分钟阶段结束

  # 自然音主题
  nature:
    name: "自然音"
    break_start: "nature/water_drop.mp3"        # 水滴声
    break_end: "nature/wind_chime.mp3"          # 风铃声
    session_complete: "nature/forest_birds.mp3" # 森林鸟鸣
```

#### 默认设置
```yaml
default_theme: "default"    # 默认主题
default_volume: 0.7         # 默认音量
```

### 配置特点

1. **简洁性**: 每个主题只需定义3个音频文件路径
2. **直观性**: 配置结构一目了然，易于理解和修改
3. **扩展性**: 添加新主题只需增加一个主题块
4. **维护性**: 配置文件小巧，易于维护和版本控制

## 音频文件要求

### 技术规格
- **格式**: MP3
- **比特率**: 128kbps
- **采样率**: 44.1kHz
- **最大文件大小**: 500KB
- **淡入淡出**: 支持

### 音质要求
- **清晰度**: 无杂音、无失真
- **音量**: 统一标准化处理
- **时长**: 符合事件要求的时长范围
- **兼容性**: 支持所有目标平台播放

### 命名规范
```
{theme}/{category}_{name}.mp3
```

示例：
- `nature/break_start_water_drop.mp3`
- `synthetic/break_end_clear_ping.mp3`
- `asmr/session_complete_completion_whisper.mp3`

## 集成说明

### 代码集成
音频配置系统通过以下类进行管理：

1. **AudioConfig**: 配置文件加载和管理
2. **AudioConfigHelper**: 便捷的配置访问方法
3. **AppSettings**: 与现有设置系统的集成

### 使用示例
```dart
// 加载配置
await AudioConfig.instance.loadConfig();

// 获取主题列表
final themes = await AudioConfigHelper.getThemeNames();

// 获取音频文件路径
final audioPath = await AudioConfigHelper.getAudioPath(
  'nature', 'break_start', '水滴声'
);
```

## 扩展指南

### 添加新主题
1. 在`audio_config.yaml`中添加新主题定义
2. 创建对应的音频文件目录
3. 添加音频文件并更新配置
4. 在`pubspec.yaml`中添加新目录到assets

### 添加新音效
1. 将音频文件放入对应主题目录
2. 在配置文件中添加音频文件定义
3. 确保文件符合技术规格要求

### 自定义主题
用户可以通过以下方式自定义音频：
1. 导入自定义音频配置文件
2. 替换现有音频文件
3. 创建完全自定义的主题包

## 维护说明

### 配置验证
系统提供配置验证功能：
```dart
final isValid = await AudioConfigHelper.validateConfig();
```

### 错误处理
- 配置文件加载失败时回退到默认设置
- 音频文件缺失时使用备用音效
- 提供详细的错误日志用于调试

### 性能优化
- 配置文件采用单例模式，避免重复加载
- 音频文件按需加载，减少内存占用
- 支持音频文件预加载和缓存

## 注意事项

1. **版权**: 确保所有音频文件具有合法使用权
2. **文件大小**: 控制音频文件大小，避免影响应用包体积
3. **平台兼容**: 测试所有目标平台的音频播放兼容性
4. **用户体验**: 音频音量和时长应符合用户期望
5. **可访问性**: 考虑听力障碍用户的替代方案

---

*本文档将随着音频系统的更新而持续维护*
