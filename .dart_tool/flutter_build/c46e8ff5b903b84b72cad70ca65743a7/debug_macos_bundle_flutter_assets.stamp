{"inputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/.dart_tool/flutter_build/c46e8ff5b903b84b72cad70ca65743a7/App.framework/App", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/Work/cursor/learnfocus/.dart_tool/flutter_build/c46e8ff5b903b84b72cad70ca65743a7/app.dill", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/development/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/Work/cursor/learnfocus/pubspec.yaml", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/.DS_Store", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/README.md", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/audio_config.yaml", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/default/session_complete.mp3", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/default/break_start.wav", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/default/placeholder.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/default/break_end.wav", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/nature/placeholder.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/synthetic/placeholder.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/assets/audio/asmr/placeholder.txt", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers-6.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_android-5.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_darwin-6.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_linux-4.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_platform_interface-7.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_web-5.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/audioplayers_windows-4.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/fake_async-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/http-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/lints-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/matcher-0.12.16+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/meta-1.15.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.15/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/synchronized-3.3.0+3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/test_api-0.7.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/vm_service-14.2.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/web-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/yaml-3.1.3/LICENSE", "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "/Users/<USER>/Documents/Work/cursor/learnfocus/DOES_NOT_EXIST_RERUN_FOR_WILDCARD134058736"], "outputs": ["/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/App", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/Info.plist", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/kernel_blob.bin", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/vm_snapshot_data", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/.DS_Store", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/README.md", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/audio_config.yaml", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/default/session_complete.mp3", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/default/break_start.wav", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/default/placeholder.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/default/break_end.wav", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/nature/placeholder.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/synthetic/placeholder.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/assets/audio/asmr/placeholder.txt", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/AssetManifest.bin", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/FontManifest.json", "/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/App.framework/Versions/A/Resources/flutter_assets/NOTICES.Z"]}