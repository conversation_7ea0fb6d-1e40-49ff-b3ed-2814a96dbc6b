import 'package:flutter/services.dart';
import '../models/settings.dart';
import '../models/audio_config.dart';

/// 音频播放服务
/// 负责管理应用中的音频播放功能
class AudioService {
  static AudioService? _instance;
  static AudioService get instance => _instance ??= AudioService._();

  AudioService._();

  bool _isInitialized = false;

  /// 初始化音频服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 加载音频配置
      await AudioConfig.instance.loadConfig();
      _isInitialized = true;
    } catch (e) {
      // 如果配置加载失败，仍然标记为已初始化，使用默认行为
      _isInitialized = true;
      print('Audio service initialized with fallback mode: $e');
    }
  }

  /// 播放小休息开始音效
  Future<void> playBreakStartSound(AppSettings settings) async {
    await _playSound(settings, 'break_start');
  }

  /// 播放小休息结束音效
  Future<void> playBreakEndSound(AppSettings settings) async {
    await _playSound(settings, 'break_end');
  }

  /// 播放阶段完成音效
  Future<void> playSessionCompleteSound(AppSettings settings) async {
    await _playSound(settings, 'session_complete');
  }

  /// 通用音频播放方法
  Future<void> _playSound(AppSettings settings, String eventType) async {
    try {
      // 确保服务已初始化
      await initialize();

      // 尝试获取音频文件路径
      String? audioPath;
      try {
        audioPath = await settings.getAudioPath(eventType);
      } catch (e) {
        print('Failed to get audio path for $eventType: $e');
        // 配置加载失败，直接使用默认音效
        audioPath = null;
      }

      if (audioPath != null) {
        // 使用配置的音频文件
        await _playAudioFile(audioPath, settings.soundVolume);
      } else {
        // 回退到默认音频文件
        await _playDefaultSound(eventType, settings.soundVolume);
      }

      // 如果启用了振动，触发振动反馈
      if (settings.enableVibration) {
        await _triggerVibration(eventType);
      }

    } catch (e) {
      print('Failed to play sound for $eventType: $e');
      // 最后的回退：尝试播放系统默认音效
      try {
        await _playDefaultSound(eventType, settings.soundVolume);
      } catch (fallbackError) {
        print('Even fallback sound failed: $fallbackError');
      }
    }
  }

  /// 播放音频文件
  Future<void> _playAudioFile(String audioPath, double volume) async {
    try {
      // 这里应该使用音频播放插件，如 audioplayers
      // 由于当前没有添加音频播放依赖，这里只是示例代码
      print('Playing audio: $audioPath with volume: $volume');

      // TODO: 实际的音频播放实现
      // final player = AudioPlayer();
      // await player.setVolume(volume);
      // await player.play(AssetSource(audioPath));

    } catch (e) {
      throw Exception('Failed to play audio file: $e');
    }
  }

  /// 播放默认音效（回退方案）
  Future<void> _playDefaultSound(String eventType, double volume) async {
    try {
      // 使用系统默认音效作为回退
      switch (eventType) {
        case 'break_start':
          await SystemSound.play(SystemSoundType.click);
          break;
        case 'break_end':
          await SystemSound.play(SystemSoundType.click);
          break;
        case 'session_complete':
          await SystemSound.play(SystemSoundType.alert);
          break;
        default:
          await SystemSound.play(SystemSoundType.click);
      }
    } catch (e) {
      print('Failed to play default sound: $e');
    }
  }

  /// 触发振动反馈
  Future<void> _triggerVibration(String eventType) async {
    try {
      switch (eventType) {
        case 'break_start':
          // 轻微振动
          await HapticFeedback.lightImpact();
          break;
        case 'break_end':
          // 中等振动
          await HapticFeedback.mediumImpact();
          break;
        case 'session_complete':
          // 强烈振动
          await HapticFeedback.heavyImpact();
          break;
        default:
          await HapticFeedback.lightImpact();
      }
    } catch (e) {
      print('Failed to trigger vibration: $e');
    }
  }

  /// 预加载音频文件
  Future<void> preloadAudio(AppSettings settings) async {
    try {
      await initialize();

      // 预加载当前设置中配置的音频文件
      final breakStartPath = await settings.getAudioPath('break_start');
      final breakEndPath = await settings.getAudioPath('break_end');
      final sessionCompletePath = await settings.getAudioPath('session_complete');

      // TODO: 实际的音频预加载实现
      print('Preloading audio files for theme: ${settings.audioTheme}');
      print('  Break start: $breakStartPath');
      print('  Break end: $breakEndPath');
      print('  Session complete: $sessionCompletePath');

    } catch (e) {
      print('Failed to preload audio: $e');
    }
  }

  /// 测试音频播放
  Future<void> testAudio(AppSettings settings, String eventType) async {
    await _playSound(settings, eventType);
  }

  /// 验证音频文件是否存在
  Future<bool> validateAudioFile(String audioPath) async {
    try {
      await rootBundle.load(audioPath);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取音频文件信息
  Future<Map<String, dynamic>?> getAudioInfo(String audioPath) async {
    try {
      final data = await rootBundle.load(audioPath);
      return {
        'path': audioPath,
        'size': data.lengthInBytes,
        'exists': true,
      };
    } catch (e) {
      return {
        'path': audioPath,
        'size': 0,
        'exists': false,
        'error': e.toString(),
      };
    }
  }

  /// 清理资源
  void dispose() {
    // TODO: 清理音频播放器资源
    _isInitialized = false;
  }
}

/// 音频服务助手类
class AudioServiceHelper {
  /// 初始化音频服务
  static Future<void> initialize() async {
    await AudioService.instance.initialize();
  }

  /// 播放事件音效
  static Future<void> playEventSound(AppSettings settings, String eventType) async {
    switch (eventType) {
      case 'break_start':
        await AudioService.instance.playBreakStartSound(settings);
        break;
      case 'break_end':
        await AudioService.instance.playBreakEndSound(settings);
        break;
      case 'session_complete':
        await AudioService.instance.playSessionCompleteSound(settings);
        break;
      default:
        print('Unknown event type: $eventType');
    }
  }

  /// 预加载音频
  static Future<void> preloadAudio(AppSettings settings) async {
    await AudioService.instance.preloadAudio(settings);
  }

  /// 测试音频
  static Future<void> testAudio(AppSettings settings, String eventType) async {
    await AudioService.instance.testAudio(settings, eventType);
  }

  /// 验证音频配置
  static Future<Map<String, bool>> validateAudioConfig(AppSettings settings) async {
    final results = <String, bool>{};

    try {
      final breakStartPath = await settings.getAudioPath('break_start');
      final breakEndPath = await settings.getAudioPath('break_end');
      final sessionCompletePath = await settings.getAudioPath('session_complete');

      if (breakStartPath != null) {
        results['break_start'] = await AudioService.instance.validateAudioFile(breakStartPath);
      }

      if (breakEndPath != null) {
        results['break_end'] = await AudioService.instance.validateAudioFile(breakEndPath);
      }

      if (sessionCompletePath != null) {
        results['session_complete'] = await AudioService.instance.validateAudioFile(sessionCompletePath);
      }

    } catch (e) {
      print('Failed to validate audio config: $e');
    }

    return results;
  }
}
