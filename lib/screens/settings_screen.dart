import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../models/settings.dart';

// 设置界面
class SettingsScreen extends StatefulWidget {
  final AppSettings settings;
  final Function(AppSettings) onSettingsChanged;

  const SettingsScreen({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
  });

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late AppSettings _currentSettings;

  @override
  void initState() {
    super.initState();
    _currentSettings = widget.settings;
  }

  void _updateSettings(AppSettings newSettings) {
    setState(() {
      _currentSettings = newSettings;
    });
    widget.onSettingsChanged(newSettings);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(AppConstants.backgroundColor),
      body: SafeArea(
        child: Column(
          children: [
            // 标题栏
            _buildHeader(),

            // 设置内容
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                child: Column(
                  children: [
                    _buildTimeSettingsCard(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildSoundSettingsCard(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '设置',
            style: TextStyle(
              fontSize: AppConstants.titleFontSize,
              fontWeight: FontWeight.bold,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSettingsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '专注时间参数设置',
            style: TextStyle(
              fontSize: AppConstants.subtitleFontSize,
              fontWeight: FontWeight.bold,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // 专注周期总时长
          _buildDropdownSetting(
            '专注周期总时长',
            '${_currentSettings.focusDurationMinutes} 分钟',
            AppSettings.focusDurationOptions.map((duration) => '$duration 分钟').toList(),
            (value) {
              final minutes = int.parse(value.split(' ')[0]);
              _updateSettings(_currentSettings.copyWith(focusDurationMinutes: minutes));
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // 小休触发频率
          _buildSectionTitle('小休触发频率:'),
          const SizedBox(height: AppConstants.paddingSmall),
          _buildRadioOption(
            '随机 (${_currentSettings.minBreakIntervalMinutes}~${_currentSettings.maxBreakIntervalMinutes}分钟)',
            _currentSettings.useRandomBreakInterval,
            (value) => _updateSettings(_currentSettings.copyWith(useRandomBreakInterval: value)),
          ),
          _buildRadioOption(
            '固定 ${_currentSettings.fixedBreakIntervalMinutes} 分钟',
            !_currentSettings.useRandomBreakInterval,
            (value) => _updateSettings(_currentSettings.copyWith(useRandomBreakInterval: !value)),
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // 小休时长
          _buildSectionTitle('小休时长:'),
          const SizedBox(height: AppConstants.paddingSmall),
          _buildRadioOption(
            '随机 (${_currentSettings.minBreakDurationSeconds}~${_currentSettings.maxBreakDurationSeconds}秒)',
            _currentSettings.useRandomBreakDuration,
            (value) => _updateSettings(_currentSettings.copyWith(useRandomBreakDuration: value)),
          ),
          _buildRadioOption(
            '固定 ${_currentSettings.fixedBreakDurationSeconds} 秒',
            !_currentSettings.useRandomBreakDuration,
            (value) => _updateSettings(_currentSettings.copyWith(useRandomBreakDuration: !value)),
          ),
        ],
      ),
    );
  }

  Widget _buildSoundSettingsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: const Color(AppConstants.cardColor),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '提示音设置',
            style: TextStyle(
              fontSize: AppConstants.subtitleFontSize,
              fontWeight: FontWeight.bold,
              color: const Color(AppConstants.textPrimaryColor),
            ),
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // 音频主题
          _buildDropdownSetting(
            '音频主题',
            _getThemeDisplayName(_currentSettings.audioTheme),
            _getThemeDisplayNames(),
            (value) {
              final themeId = _getThemeIdFromDisplayName(value);
              _updateSettings(_currentSettings.copyWith(audioTheme: themeId));
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // 导入提示音配置按钮
          SizedBox(
            width: double.infinity,
            height: 45,
            child: OutlinedButton(
              onPressed: () {
                // TODO: 实现导入提示音配置功能
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('导入功能开发中...')),
                );
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(AppConstants.primaryColor),
                side: const BorderSide(color: Color(AppConstants.primaryColor)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                ),
              ),
              child: const Text(
                '导入提示音配置',
                style: TextStyle(
                  fontSize: AppConstants.bodyFontSize,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: AppConstants.bodyFontSize,
        fontWeight: FontWeight.w600,
        color: const Color(AppConstants.textPrimaryColor),
      ),
    );
  }

  Widget _buildDropdownSetting(
    String label,
    String currentValue,
    List<String> options,
    Function(String) onChanged,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '$label:',
          style: TextStyle(
            fontSize: AppConstants.bodyFontSize,
            color: const Color(AppConstants.textPrimaryColor),
          ),
        ),
        DropdownButton<String>(
          value: currentValue,
          items: options.map((option) {
            return DropdownMenuItem<String>(
              value: option,
              child: Text(option),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onChanged(value);
            }
          },
          underline: Container(),
          style: TextStyle(
            fontSize: AppConstants.bodyFontSize,
            color: const Color(AppConstants.primaryColor),
          ),
        ),
      ],
    );
  }

  Widget _buildRadioOption(String title, bool value, Function(bool) onChanged) {
    return GestureDetector(
      onTap: () => onChanged(!value),
      child: Row(
        children: [
          Radio<bool>(
            value: true,
            groupValue: value,
            onChanged: (val) => onChanged(val ?? false),
            activeColor: const Color(AppConstants.primaryColor),
          ),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: AppConstants.bodyFontSize,
                color: const Color(AppConstants.textPrimaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 获取主题显示名称
  String _getThemeDisplayName(String themeId) {
    switch (themeId) {
      case 'default':
        return '默认';
      case 'nature':
        return '自然音';
      case 'synthetic':
        return '合成音';
      case 'asmr':
        return 'ASMR';
      default:
        return '默认';
    }
  }

  // 获取所有主题显示名称
  List<String> _getThemeDisplayNames() {
    return ['默认', '自然音', '合成音', 'ASMR'];
  }

  // 从显示名称获取主题ID
  String _getThemeIdFromDisplayName(String displayName) {
    switch (displayName) {
      case '默认':
        return 'default';
      case '自然音':
        return 'nature';
      case '合成音':
        return 'synthetic';
      case 'ASMR':
        return 'asmr';
      default:
        return 'default';
    }
  }
}
