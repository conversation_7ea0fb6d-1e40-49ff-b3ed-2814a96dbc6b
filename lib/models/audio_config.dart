import 'package:flutter/services.dart';
import 'package:yaml/yaml.dart';

/// 简化的音频配置管理类
class AudioConfig {
  static AudioConfig? _instance;
  static AudioConfig get instance => _instance ??= AudioConfig._();
  
  AudioConfig._();

  Map<String, dynamic>? _config;
  bool _isLoaded = false;

  /// 加载音频配置
  Future<void> loadConfig() async {
    if (_isLoaded) return;
    
    try {
      final yamlString = await rootBundle.loadString('assets/audio/audio_config.yaml');
      final yamlMap = loadYaml(yamlString);
      _config = Map<String, dynamic>.from(yamlMap);
      _isLoaded = true;
    } catch (e) {
      throw Exception('Failed to load audio config: $e');
    }
  }

  /// 确保配置已加载
  void _ensureLoaded() {
    if (!_isLoaded || _config == null) {
      throw Exception('Audio config not loaded. Call loadConfig() first.');
    }
  }

  /// 获取所有音频主题
  List<AudioTheme> getThemes() {
    _ensureLoaded();
    final themes = <AudioTheme>[];
    final themesData = _config!['themes'] as Map<String, dynamic>;
    
    for (final entry in themesData.entries) {
      themes.add(AudioTheme.fromMap(entry.key, entry.value));
    }
    
    return themes;
  }

  /// 根据主题ID获取主题
  AudioTheme? getTheme(String themeId) {
    _ensureLoaded();
    final themesData = _config!['themes'] as Map<String, dynamic>;
    final themeData = themesData[themeId];
    
    if (themeData == null) return null;
    return AudioTheme.fromMap(themeId, themeData);
  }

  /// 获取默认主题
  String getDefaultTheme() {
    _ensureLoaded();
    return _config!['default_theme'] ?? 'default';
  }

  /// 获取默认音量
  double getDefaultVolume() {
    _ensureLoaded();
    return (_config!['default_volume'] ?? 0.7).toDouble();
  }

  /// 获取音频文件路径
  String? getAudioPath(String themeId, String eventType) {
    final theme = getTheme(themeId);
    if (theme == null) return null;
    
    final relativePath = theme.getAudioPath(eventType);
    if (relativePath == null) return null;
    
    return 'assets/audio/$relativePath';
  }
}

/// 简化的音频主题模型
class AudioTheme {
  final String id;
  final String name;
  final String breakStart;
  final String breakEnd;
  final String sessionComplete;

  AudioTheme({
    required this.id,
    required this.name,
    required this.breakStart,
    required this.breakEnd,
    required this.sessionComplete,
  });

  factory AudioTheme.fromMap(String id, Map<String, dynamic> map) {
    return AudioTheme(
      id: id,
      name: map['name'] ?? '',
      breakStart: map['break_start'] ?? '',
      breakEnd: map['break_end'] ?? '',
      sessionComplete: map['session_complete'] ?? '',
    );
  }

  /// 根据事件类型获取音频文件路径
  String? getAudioPath(String eventType) {
    switch (eventType) {
      case 'break_start':
        return breakStart;
      case 'break_end':
        return breakEnd;
      case 'session_complete':
        return sessionComplete;
      default:
        return null;
    }
  }
}

/// 音频配置助手类
class AudioConfigHelper {
  /// 获取所有可用的音频主题
  static Future<List<AudioTheme>> getThemes() async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getThemes();
  }

  /// 获取所有主题名称
  static Future<List<String>> getThemeNames() async {
    final themes = await getThemes();
    return themes.map((theme) => theme.name).toList();
  }

  /// 获取所有主题ID
  static Future<List<String>> getThemeIds() async {
    final themes = await getThemes();
    return themes.map((theme) => theme.id).toList();
  }

  /// 根据主题ID获取主题
  static Future<AudioTheme?> getTheme(String themeId) async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getTheme(themeId);
  }

  /// 获取音频文件路径
  static Future<String?> getAudioPath(String themeId, String eventType) async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getAudioPath(themeId, eventType);
  }

  /// 获取默认主题
  static Future<String> getDefaultTheme() async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getDefaultTheme();
  }

  /// 获取默认音量
  static Future<double> getDefaultVolume() async {
    await AudioConfig.instance.loadConfig();
    return AudioConfig.instance.getDefaultVolume();
  }

  /// 验证音频配置是否有效
  static Future<bool> validateConfig() async {
    try {
      await AudioConfig.instance.loadConfig();
      final themes = AudioConfig.instance.getThemes();
      return themes.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
