import 'package:flutter/material.dart';
import 'utils/constants.dart';
import 'models/focus_session.dart';
import 'models/settings.dart';
import 'models/statistics.dart';
import 'screens/home_screen.dart';
import 'screens/focus_screen.dart';
import 'screens/statistics_screen.dart';
import 'screens/settings_screen.dart';
import 'widgets/bottom_navigation.dart';
import 'services/audio_service.dart';

void main() {
  runApp(const InterflowApp());
}

class InterflowApp extends StatelessWidget {
  const InterflowApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '间歇流',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(AppConstants.primaryColor),
        ),
        useMaterial3: true,
        fontFamily: 'PingFang SC',
      ),
      debugShowCheckedModeBanner: false,
      home: const MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  AppState _appState = AppState.idle;

  // 数据模型
  late AppSettings _settings;
  late StatisticsData _statisticsData;
  FocusSession? _currentSession;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    _settings = AppSettings();
    _statisticsData = StatisticsData();

    // 异步初始化音频服务，不阻塞UI
    _initializeAudioService();

    // TODO: 从本地存储加载数据
    // _loadDataFromStorage();
  }

  /// 异步初始化音频服务
  Future<void> _initializeAudioService() async {
    try {
      // 先初始化音频服务
      await AudioService.instance.initialize();
      print('Audio service initialized successfully');

      // 预加载音频文件
      await AudioService.instance.preloadAudio(_settings);
      print('Audio files preloaded successfully');

      // 测试音频配置加载
      final testPath = await _settings.getAudioPath('break_end');
      print('Test audio path for break_end: $testPath');

      // 验证音频文件是否存在
      if (testPath != null) {
        final exists = await AudioService.instance.validateAudioFile(testPath);
        print('Audio file exists: $exists');
      }
    } catch (e) {
      print('Audio service initialization failed: $e');
      // 初始化失败不影响应用启动
    }
  }

  void _startFocusSession() async {
    // 检查widget是否仍然mounted
    if (!mounted) return;

    final session = FocusSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      startTime: DateTime.now(),
      durationMinutes: _settings.focusDurationMinutes,
    );

    setState(() {
      _currentSession = session;
      _appState = AppState.focusing;
    });

    // 导航到专注界面（通过覆盖当前界面）
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FocusScreen(
          session: session,
          settings: _settings,
          onPause: () {
            setState(() {
              _appState = AppState.idle;
            });
          },
          onResume: () {
            setState(() {
              _appState = AppState.focusing;
            });
          },
          onStop: () {
            setState(() {
              _currentSession = null;
              _appState = AppState.idle;
            });
            Navigator.of(context).pop();
          },
          onBreakTriggered: (breakRecord) {
            // 处理休息触发
          },
          onSessionComplete: () {
            if (_currentSession != null) {
              _statisticsData.addSession(_currentSession!);
              setState(() {
                _currentSession = null;
                _appState = AppState.completed;
              });
            }
            Navigator.of(context).pop();
            _showCompletionDialog();
          },
        ),
      ),
    );
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('专注完成！'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('恭喜完成90分钟专注'),
            const SizedBox(height: 16),
            const Text('现在开始20分钟完整休息'),
            const SizedBox(height: 16),
            if (_currentSession != null) ...[
              Text('本次专注数据统计:'),
              Text('小休次数: ${_currentSession!.totalBreaks}次'),
              Text('平均间隔: ${_currentSession!.averageBreakInterval.toStringAsFixed(1)}分钟'),
              Text('总休息时长: ${_currentSession!.totalBreakDuration}秒'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _appState = AppState.idle;
              });
            },
            child: const Text('知道了'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 开始休息计时
              setState(() {
                _appState = AppState.longBreak;
              });
            },
            child: const Text('开始休息计时'),
          ),
        ],
      ),
    );
  }

  void _onTabChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _onSettingsChanged(AppSettings newSettings) {
    setState(() {
      _settings = newSettings;
    });
    // TODO: 保存设置到本地存储
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: [
          // 主页
          HomeScreen(
            statisticsData: _statisticsData,
            settings: _settings,
            onStartFocus: _startFocusSession,
          ),

          // 统计页面
          StatisticsScreen(
            statisticsData: _statisticsData,
          ),

          // 设置页面
          SettingsScreen(
            settings: _settings,
            onSettingsChanged: _onSettingsChanged,
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentIndex,
        onTap: _onTabChanged,
      ),
    );
  }
}
