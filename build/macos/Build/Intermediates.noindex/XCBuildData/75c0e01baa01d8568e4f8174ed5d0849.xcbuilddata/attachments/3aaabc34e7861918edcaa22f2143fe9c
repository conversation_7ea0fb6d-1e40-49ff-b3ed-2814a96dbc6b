-target arm64-apple-macos10.14 '-std=gnu11' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/ModuleCache.noindex' '-fmodule-name=path_provider_foundation' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -g -iquote /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/path_provider_foundation-generated-files.hmap -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/path_provider_foundation-own-target-headers.hmap -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/path_provider_foundation-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-8699adb1dd336b26511df848a716bd42-VFS/all-product-headers.yaml -iquote /Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/path_provider_foundation-project-headers.hmap -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation/include -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/DerivedSources-normal/arm64 -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/DerivedSources/arm64 -I/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Intermediates.noindex/Pods.build/Debug/path_provider_foundation.build/DerivedSources -F/Users/<USER>/Documents/Work/cursor/learnfocus/build/macos/Build/Products/Debug/path_provider_foundation -F/Users/<USER>/development/flutter/bin/cache/artifacts/engine/darwin-x64/FlutterMacOS.xcframework/macos-arm64_x86_64